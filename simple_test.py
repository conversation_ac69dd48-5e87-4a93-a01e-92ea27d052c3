#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 简单的 FC 3.0 测试脚本

import os
import sys
import base64
import zipfile
import io

# 尝试导入配置文件
try:
    import config
    ACCESS_KEY_ID = config.ALIBABA_CLOUD_ACCESS_KEY_ID
    ACCESS_KEY_SECRET = config.ALIBABA_CLOUD_ACCESS_KEY_SECRET
    REGION = getattr(config, 'REGION', 'cn-shanghai')
except ImportError:
    # 如果没有配置文件，从环境变量读取
    ACCESS_KEY_ID = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID')
    ACCESS_KEY_SECRET = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET')
    REGION = os.environ.get('ALIBABA_CLOUD_REGION', 'cn-shanghai')

from alibabacloud_fc20230330.client import Client as FC20230330Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_fc20230330 import models as fc_20230330_models


def create_simple_zip():
    """
    创建一个简单的 Python 函数 ZIP 包
    """
    # 简单的 Hello World 函数
    function_code = '''def handler(event, context):
    return {
        'statusCode': 200,
        'body': 'Hello from FC 3.0!'
    }
'''
    
    # 创建 ZIP 文件
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        zip_file.writestr('index.py', function_code)
    
    # 返回 base64 编码的 ZIP 内容
    zip_buffer.seek(0)
    return base64.b64encode(zip_buffer.read()).decode('utf-8')


def test_fc_connection():
    """
    测试 FC 连接和 API 调用
    """
    print(f"凭据检查: ACCESS_KEY_ID: {ACCESS_KEY_ID}")
    print(f"凭据检查: ACCESS_KEY_SECRET: {'***' if ACCESS_KEY_SECRET else None}")
    print(f"区域: {REGION}")
    
    if not ACCESS_KEY_ID or not ACCESS_KEY_SECRET:
        print("\n❌ 错误: 请设置阿里云访问凭据!")
        print("方法1: 创建 config.py 文件并填入凭据")
        print("方法2: 设置环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET")
        return False
    
    # 创建客户端
    config = open_api_models.Config(
        access_key_id=ACCESS_KEY_ID,
        access_key_secret=ACCESS_KEY_SECRET
    )
    config.endpoint = f'{REGION}.fc.aliyuncs.com'
    client = FC20230330Client(config)
    
    print(f"\n🔗 连接到: {config.endpoint}")
    
    # 测试 ListFunctions
    print("\n=== 测试 ListFunctions API ===")
    try:
        response = client.list_functions()
        print("✅ ListFunctions 调用成功!")
        print(f"状态码: {response.status_code}")
        if hasattr(response.body, 'functions') and response.body.functions:
            print(f"现有函数数量: {len(response.body.functions)}")
            for func in response.body.functions[:3]:  # 只显示前3个
                print(f"  - {func.function_name}")
        else:
            print("当前没有函数")
    except Exception as e:
        print(f"❌ ListFunctions 调用失败: {e}")
        return False
    
    # 测试 CreateFunction
    print("\n=== 测试 CreateFunction API ===")
    try:
        # 创建简单的 ZIP 包
        zip_content = create_simple_zip()
        
        # 创建函数请求
        create_function_input = fc_20230330_models.CreateFunctionInput(
            function_name='test-hello-world',
            description='Simple test function created via FC 3.0 SDK',
            runtime='python3.9',
            handler='index.handler',
            memory_size=128,
            timeout=30,
            code=fc_20230330_models.InputCodeLocation(
                zip_file=zip_content
            )
        )
        
        create_function_request = fc_20230330_models.CreateFunctionRequest(
            body=create_function_input
        )
        
        response = client.create_function(create_function_request)
        print("✅ CreateFunction 调用成功!")
        print(f"状态码: {response.status_code}")
        if hasattr(response.body, 'function_name'):
            print(f"函数名: {response.body.function_name}")
            print(f"函数 ARN: {getattr(response.body, 'function_arn', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ CreateFunction 调用失败: {e}")
        print(f"错误类型: {type(e)}")
        
        # 如果是函数已存在的错误，这实际上是好事
        if "AlreadyExists" in str(e) or "already exists" in str(e):
            print("💡 函数已存在，这说明之前的创建是成功的！")
            return True
        
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = test_fc_connection()
    if success:
        print("\n🎉 测试完成！FC 3.0 API 调用成功！")
    else:
        print("\n💥 测试失败，请检查配置和权限")
        sys.exit(1)
