#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 验证代码结构是否正确（不实际调用 API）

import sys

def validate_imports():
    """验证所有必要的导入是否正确"""
    print("🔍 验证导入...")
    
    try:
        from alibabacloud_fc20230330.client import Client as FC20230330Client
        print("✅ FC20230330Client 导入成功")
    except ImportError as e:
        print(f"❌ FC20230330Client 导入失败: {e}")
        return False
    
    try:
        from alibabacloud_fc20230330 import models as fc_20230330_models
        print("✅ fc_20230330_models 导入成功")
    except ImportError as e:
        print(f"❌ fc_20230330_models 导入失败: {e}")
        return False
    
    try:
        from alibabacloud_tea_openapi import models as open_api_models
        print("✅ open_api_models 导入成功")
    except ImportError as e:
        print(f"❌ open_api_models 导入失败: {e}")
        return False
    
    return True

def validate_models():
    """验证模型结构是否正确"""
    print("\n🔍 验证模型结构...")
    
    try:
        from alibabacloud_fc20230330 import models as fc_20230330_models
        
        # 验证 ListFunctionsRequest
        list_request = fc_20230330_models.ListFunctionsRequest()
        print("✅ ListFunctionsRequest 创建成功")
        
        # 验证 CreateFunctionInput
        create_input = fc_20230330_models.CreateFunctionInput(
            function_name='test-function',
            runtime='python3.9',
            handler='index.handler'
        )
        print("✅ CreateFunctionInput 创建成功")
        
        # 验证 InputCodeLocation
        code_location = fc_20230330_models.InputCodeLocation(
            zip_file='test_base64_content'
        )
        print("✅ InputCodeLocation 创建成功")
        
        # 验证 CreateFunctionRequest
        create_request = fc_20230330_models.CreateFunctionRequest(
            body=create_input
        )
        print("✅ CreateFunctionRequest 创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型验证失败: {e}")
        return False

def validate_client_creation():
    """验证客户端创建是否正确"""
    print("\n🔍 验证客户端创建...")
    
    try:
        from alibabacloud_fc20230330.client import Client as FC20230330Client
        from alibabacloud_tea_openapi import models as open_api_models
        
        # 使用测试凭据创建配置
        config = open_api_models.Config(
            access_key_id="test_key",
            access_key_secret="test_secret"
        )
        config.endpoint = "cn-shanghai.fc.aliyuncs.com"
        
        # 创建客户端（不会实际连接）
        client = FC20230330Client(config)
        print("✅ FC20230330Client 创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 开始验证阿里云 FC 3.0 SDK 代码结构...")
    
    all_passed = True
    
    # 验证导入
    if not validate_imports():
        all_passed = False
    
    # 验证模型
    if not validate_models():
        all_passed = False
    
    # 验证客户端创建
    if not validate_client_creation():
        all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有验证通过！代码结构正确！")
        print("\n✅ 您的代码现在可以正确调用阿里云 FC 3.0 API")
        print("✅ 只需要设置正确的 AccessKey 凭据即可使用")
        print("\n📝 下一步:")
        print("1. 复制 config_example.py 为 config.py")
        print("2. 在 config.py 中填入您的真实 AccessKey")
        print("3. 运行 python simple_test.py")
    else:
        print("❌ 验证失败！请检查 SDK 安装")
        print("\n🔧 修复建议:")
        print("pip install alibabacloud_fc20230330")
        sys.exit(1)

if __name__ == '__main__':
    main()
