#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 测试 UpdateFunction API

import os
import sys
import base64
import zipfile
import io



from alibabacloud_fc20230330.client import Client as FC20230330Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_fc20230330 import models as fc_20230330_models


def create_client():
    """创建 FC 客户端"""
    config = open_api_models.Config(
          # 必填，您的 AccessKey ID
        access_key_id="LTAI5tEpbatj9UoMUDVpV3hX",
        # 必填，您的 AccessKey Secret
        access_key_secret="******************************"
    )
    config.endpoint ="fcv3.cn-shanghai.aliyuncs.com"
    return FC20230330Client(config)

def create_comomn():
    function_code = '''import mysql.connector
from typing import List, Dict, Any
import logging

class StarRocksConnector:
    def __init__(self, host: str, port: int, user: str, password: str, database: str):
        """
        初始化StarRocks连接器
        
        Args:
            host: StarRocks主机地址
            port: 端口号
            user: 用户名
            password: 密码
            database: 数据库名
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None

    def connect(self) -> None:
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database
            )
            logging.info("Successfully connected to StarRocks database")
        except Exception as e:
            logging.error(f"Error connecting to StarRocks: {str(e)}")
            raise

    def disconnect(self) -> None:
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logging.info("Database connection closed")

    def query(self, sql: str) -> List[Dict[str, Any]]:
        """
        执行SQL查询并返回结果
        
        Args:
            sql: SQL查询语句
            
        Returns:
            List[Dict]: 查询结果列表，每个字典代表一行数据
        """
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(sql)
            results = cursor.fetchall()
            cursor.close()
            return results
        except Exception as e:
            logging.error(f"Error executing query: {str(e)}")
            raise
        
    def __enter__(self):
        """支持with语句的上下文管理"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持with语句的上下文管理"""
        self.disconnect()

# 使用示例
if __name__ == "__main__":
    # 配置数据库连接信息
    db_config = {
        "host": "your_starrocks_host",
        "port": 9030,  # StarRocks默认端口
        "user": "your_username",
        "password": "your_password",
        "database": "your_database"
    }
    
    # 使用with语句自动管理连接
    with StarRocksConnector(**db_config) as sr:
        # 执行查询示例
        try:
            # 查询示例
            sql = "SELECT * FROM your_table LIMIT 10"
            results = sr.query(sql)
            
            # 打印结果
            for row in results:
                print(row)
                
        except Exception as e:
            print(f"Query failed: {str(e)}")
'''
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        zip_file.writestr('common.py', function_code)
    zip_buffer.seek(0)
    return base64.b64encode(zip_buffer.read()).decode('utf-8')



def create_simple_zip():
    """创建一个简单的 Python 函数 ZIP 包"""
    function_code = '''def handler(event, context):
    return {
        'statusCode': 200,
        'body': 'Hello from Updated Function!'
    }
'''
    
    # 创建 ZIP 文件
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        zip_file.writestr('index.py', function_code)
    
    # 返回 base64 编码的 ZIP 内容
    zip_buffer.seek(0)
    return base64.b64encode(zip_buffer.read()).decode('utf-8')


def test_update_function():
    
    client = create_client()
    zip_content = create_simple_zip()
    
    # 要更新的函数名
    function_name = 'my-test-function-sdk'
    
    print(f"\n📝 准备更新函数: {function_name}")
    
    # 创建更新函数的输入（不包含 function_name）
    update_function_input = fc_20230330_models.UpdateFunctionInput(
        description='Updated test function via FC SDK - New Version',
        runtime='python3.9',
        handler='index.handler',
        memory_size=256,  # 更新内存大小从 128 到 256
        timeout=90,       # 更新超时时间从 60 到 90
        code=fc_20230330_models.InputCodeLocation(
            zip_file=zip_content
        )
    )
    
    try:
        # 创建 UpdateFunction 请求
        update_function_request = fc_20230330_models.UpdateFunctionRequest(
            body=update_function_input
        )
        
        print(f"\n🚀 调用 UpdateFunction API...")
        print(f"函数名: {function_name}")
        print(f"新内存大小: 256 MB")
        print(f"新超时时间: 90 秒")
        
        # 调用 update_function 方法，函数名作为第一个参数
        response = client.update_function(function_name, update_function_request)
        
        print("\n✅ UpdateFunction 调用成功!")
        print(f"状态码: {response.status_code}")
        print(f"更新的函数: {function_name}")
        
        if hasattr(response.body, 'function_name'):
            print(f"函数名: {response.body.function_name}")
            print(f"描述: {getattr(response.body, 'description', 'N/A')}")
            print(f"运行时: {getattr(response.body, 'runtime', 'N/A')}")
            print(f"内存大小: {getattr(response.body, 'memory_size', 'N/A')} MB")
            print(f"超时时间: {getattr(response.body, 'timeout', 'N/A')} 秒")
            print(f"处理器: {getattr(response.body, 'handler', 'N/A')}")
            
            if hasattr(response.body, 'last_modified_time'):
                print(f"最后修改时间: {response.body.last_modified_time}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ UpdateFunction 调用失败: {e}")
        print(f"错误类型: {type(e)}")
        
        # 检查常见错误
        error_str = str(e)
        if "FunctionNotFound" in error_str:
            print("💡 提示: 函数不存在，请先创建函数")
        elif "InvalidParameter" in error_str:
            print("💡 提示: 参数无效，请检查函数配置")
        elif "AccessDenied" in error_str:
            print("💡 提示: 权限不足，请检查 AccessKey 权限")
        elif "SignatureNotMatch" in error_str:
            print("💡 提示: 签名错误，请检查 AccessKey 配置")
        
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = test_update_function()
    
