import mysql.connector
from typing import List, Dict, Any
import logging

class StarRocksConnector:
    def __init__(self, host: str, port: int, user: str, password: str, database: str):
        """
        初始化StarRocks连接器
        
        Args:
            host: StarRocks主机地址
            port: 端口号
            user: 用户名
            password: 密码
            database: 数据库名
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None

    def connect(self) -> None:
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database
            )
            logging.info("Successfully connected to StarRocks database")
        except Exception as e:
            logging.error(f"Error connecting to StarRocks: {str(e)}")
            raise

    def disconnect(self) -> None:
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logging.info("Database connection closed")

    def query(self, sql: str) -> List[Dict[str, Any]]:
        """
        执行SQL查询并返回结果
        
        Args:
            sql: SQL查询语句
            
        Returns:
            List[Dict]: 查询结果列表，每个字典代表一行数据
        """
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(sql)
            results = cursor.fetchall()
            cursor.close()
            return results
        except Exception as e:
            logging.error(f"Error executing query: {str(e)}")
            raise
        
    def __enter__(self):
        """支持with语句的上下文管理"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持with语句的上下文管理"""
        self.disconnect()

# 使用示例
if __name__ == "__main__":
    # 配置数据库连接信息
    db_config = {
        "host": "your_starrocks_host",
        "port": 9030,  # StarRocks默认端口
        "user": "your_username",
        "password": "your_password",
        "database": "your_database"
    }
    
    # 使用with语句自动管理连接
    with StarRocksConnector(**db_config) as sr:
        # 执行查询示例
        try:
            # 查询示例
            sql = "SELECT * FROM your_table LIMIT 10"
            results = sr.query(sql)
            
            # 打印结果
            for row in results:
                print(row)
                
        except Exception as e:
            print(f"Query failed: {str(e)}")