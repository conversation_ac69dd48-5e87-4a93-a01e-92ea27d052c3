# 阿里云函数计算 FC 3.0 API 调试

本项目用于调试阿里云函数计算 FC 3.0 的 CreateFunction API 调用。

## 问题修复

原始代码存在以下问题，已经修复：

1. **Endpoint 格式错误**
   - 原始: `1857722393097704.cn-shanghai.fc.aliyuncs.com`
   - 修复: `cn-shanghai.fc.aliyuncs.com`

2. **API 路径错误**
   - 原始: `/2023-03-30/regions` (这是 DescribeRegions 的路径)
   - 修复: `/2023-03-30/functions` (CreateFunction 的正确路径)

3. **HTTP 方法错误**
   - 原始: `GET`
   - 修复: `POST` (CreateFunction 需要 POST 方法)

4. **认证类型设置**
   - 使用: `auth_type='AK'` 和 `style='ROA'`

5. **请求体格式**
   - 添加了完整的函数配置信息作为请求体

## 使用方法

### 方法1: 使用配置文件（推荐）

1. 复制 `config_template.py` 为 `config.py`:
   ```bash
   cp config_template.py config.py
   ```

2. 编辑 `config.py` 文件，填入您的真实凭据:
   ```python
   ALIBABA_CLOUD_ACCESS_KEY_ID = "your_real_access_key_id"
   ALIBABA_CLOUD_ACCESS_KEY_SECRET = "your_real_access_key_secret"
   REGION = "cn-shanghai"  # 或其他区域
   ```

### 方法2: 使用环境变量

```bash
export ALIBABA_CLOUD_ACCESS_KEY_ID="your_access_key_id"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your_access_key_secret"
export ALIBABA_CLOUD_REGION="cn-shanghai"
```

## 测试步骤

1. **测试 ListFunctions API**（推荐先测试）:
   ```bash
   python test_list_functions.py
   ```

2. **测试 CreateFunction API**:
   ```bash
   python demo.py
   ```

## 文件说明

- `demo.py`: 修复后的 CreateFunction API 调用示例
- `test_list_functions.py`: ListFunctions API 测试（用于验证凭据和连接）
- `config_template.py`: 配置文件模板
- `config.py`: 实际配置文件（需要自己创建）

## 注意事项

1. **安全性**: 不要将包含真实凭据的 `config.py` 文件提交到版本控制系统
2. **权限**: 确保您的 AccessKey 具有函数计算的相关权限
3. **区域**: 确保指定的区域支持函数计算服务
4. **函数名**: CreateFunction 中的函数名必须唯一

## 常见错误

1. **SignatureNotMatch**: 通常是 endpoint、认证方式或请求格式错误
2. **InvalidAccessKeyId**: AccessKey ID 错误或不存在
3. **SignatureDoesNotMatch**: AccessKey Secret 错误
4. **Forbidden**: 权限不足，需要添加函数计算相关权限

## 调试建议

1. 先运行 `test_list_functions.py` 验证基本连接
2. 检查控制台输出的详细错误信息
3. 确认 endpoint 格式正确
4. 验证 AccessKey 权限
