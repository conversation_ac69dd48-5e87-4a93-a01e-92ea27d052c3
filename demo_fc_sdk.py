# -*- coding: utf-8 -*-
# 使用正确的 FC 3.0 SDK 调用 CreateFunction API
import os
import sys
import json

from typing import List

# 尝试导入配置文件
try:
    import config
    ACCESS_KEY_ID = config.ALIBABA_CLOUD_ACCESS_KEY_ID
    ACCESS_KEY_SECRET = config.ALIBABA_CLOUD_ACCESS_KEY_SECRET
    REGION = getattr(config, 'REGION', 'cn-shanghai')
except ImportError:
    # 如果没有配置文件，从环境变量读取
    ACCESS_KEY_ID = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID')
    ACCESS_KEY_SECRET = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET')
    REGION = os.environ.get('ALIBABA_CLOUD_REGION', 'cn-shanghai')

from alibabacloud_fc20230330.client import Client as FC20230330Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_fc20230330 import models as fc_20230330_models


class FCDemo:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> FC20230330Client:
        """
        使用 AccessKey 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            # 必填，您的 AccessKey ID
            access_key_id=ACCESS_KEY_ID,
            # 必填，您的 AccessKey Secret
            access_key_secret=ACCESS_KEY_SECRET
        )
        # 访问的域名
        config.endpoint = f'{REGION}.fc.aliyuncs.com'
        return FC20230330Client(config)

    @staticmethod
    def test_list_functions():
        """
        测试 ListFunctions API
        """
        print("=== 测试 ListFunctions API ===")
        client = FCDemo.create_client()

        try:
            # 创建 ListFunctions 请求
            list_functions_request = fc_20230330_models.ListFunctionsRequest()
            response = client.list_functions(list_functions_request)
            print("ListFunctions 调用成功!")
            print(f"状态码: {response.status_code}")
            print(f"响应体: {response.body}")
            return True
        except Exception as e:
            print(f"ListFunctions 调用失败: {e}")
            return False

    @staticmethod
    def test_create_function():
        """
        测试 CreateFunction API
        """
        print("\n=== 测试 CreateFunction API ===")
        client = FCDemo.create_client()

        # 创建函数请求 - 使用正确的模型结构
        create_function_input = fc_20230330_models.CreateFunctionInput(
            function_name='my-test-function-sdk',
            description='Test function created via FC SDK',
            runtime='python3.9',
            handler='index.handler',
            memory_size=128,
            timeout=60,
            code=fc_20230330_models.InputCodeLocation(
                # 这是一个简单的 Python 函数的 base64 编码
                zip_file='UEsDBBQAAAAIAOeAjVUAAAAAAAAAAAAAAAAJAAAAaW5kZXgucHlLyczPTSxRyMkv1UvOyS/NK8nMz9FLzs8rzi8qSczLTEnNyy9KTc5ILUosKcnIzEvVy8nPS1WoVrJSULJSSuRaAQBQSwcIXKZJ2EQAAABQAAAAUFBQSWMAAAAIAA7giI1VAAAAAAAAAAAAAAAACQAAAGluZGV4LnB5S8nMz00sUcjJL9VLzskvy8zJ1EvOzyvOLypJzMtMSc3LL0pNzkgtSiwpycjMS9XLyc9LVahWslJQslJK5FoBQSwHCFymSdhEAAAAUAAAAA=='
            )
        )

        create_function_request = fc_20230330_models.CreateFunctionRequest(
            body=create_function_input
        )

        try:
            response = client.create_function(create_function_request)
            print("CreateFunction 调用成功!")
            print(f"状态码: {response.status_code}")
            print(f"函数信息: {response.body}")
            return True
        except Exception as e:
            print(f"CreateFunction 调用失败: {e}")
            print(f"错误类型: {type(e)}")
            import traceback
            traceback.print_exc()
            return False

    @staticmethod
    def main(args: List[str]) -> None:
        print(f"凭据检查: ACCESS_KEY_ID: {ACCESS_KEY_ID}")
        print(f"凭据检查: ACCESS_KEY_SECRET: {'***' if ACCESS_KEY_SECRET else None}")
        print(f"区域: {REGION}")

        if not ACCESS_KEY_ID or not ACCESS_KEY_SECRET:
            print("错误: 请设置阿里云访问凭据!")
            print("方法1: 创建 config.py 文件并填入凭据")
            print("方法2: 设置环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET")
            return

        FCDemo.test_create_function()


if __name__ == '__main__':
    FCDemo.main(sys.argv[1:])
