# -*- coding: utf-8 -*-
# 使用正确的 FC 3.0 SDK 调用 CreateFunction API
import os
import sys
import json
import base64
import zipfile
import io

from typing import List

# 尝试导入配置文件
try:
    import config
    ACCESS_KEY_ID = config.ALIBABA_CLOUD_ACCESS_KEY_ID
    ACCESS_KEY_SECRET = config.ALIBABA_CLOUD_ACCESS_KEY_SECRET
    REGION = getattr(config, 'REGION', 'fcv3.cn-shanghai')
except ImportError:
    # 如果没有配置文件，从环境变量读取
    ACCESS_KEY_ID = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID')
    ACCESS_KEY_SECRET = os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET')
    REGION = os.environ.get('ALIBABA_CLOUD_REGION', 'fcv3.cn-shanghai')

from alibabacloud_fc20230330.client import Client as FC20230330Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_fc20230330 import models as fc_20230330_models


class FCDemo:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> FC20230330Client:
        """
        使用 AccessKey 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            # 必填，您的 AccessKey ID
            access_key_id="LTAI5tEpbatj9UoMUDVpV3hX",
            # 必填，您的 AccessKey Secret
            access_key_secret="******************************"
        )
        # 访问的域名
        config.endpoint = "fcv3.cn-shanghai.aliyuncs.com"
        return FC20230330Client(config)

    @staticmethod
    def test_list_functions():
        """
        测试 ListFunctions API
        """
        print("=== 测试 ListFunctions API ===")
        client = FCDemo.create_client()

        try:
            # 创建 ListFunctions 请求
            list_functions_request = fc_20230330_models.ListFunctionsRequest()
            response = client.list_functions(list_functions_request)
            print("ListFunctions 调用成功!")
            print(f"状态码: {response.status_code}")
            if hasattr(response.body, 'functions') and response.body.functions:
                print(f"现有函数数量: {len(response.body.functions)}")
            for func in response.body.functions[:4]:  # 只显示前4个
                print(f"  - {func.function_name}")
            else:
                print("当前没有函数")
        except Exception as e:
            print(f"ListFunctions 调用失败: {e}")
            return False


    @staticmethod
    def test_create_function():
        """
        测试 CreateFunction API
        """
        print("\n=== 测试 CreateFunction API ===")
        client = FCDemo.create_client()

        # 创建函数请求 - 使用正确的模型结构
        create_function_input = fc_20230330_models.CreateFunctionInput(
            function_name='my-test-function-sdk1',
            description='Test function created via FC SDK',
            runtime='python3.9',
            handler='index.handler',
            memory_size=128,
            timeout=60,
            code=fc_20230330_models.InputCodeLocation(
                # 这是一个简单的 Python 函数的 base64 编码
                zip_file='UEsDBBQAAAAIAOeAjVUAAAAAAAAAAAAAAAAJAAAAaW5kZXgucHlLyczPTSxRyMkv1UvOyS/NK8nMz9FLzs8rzi8qSczLTEnNyy9KTc5ILUosKcnIzEvVy8nPS1WoVrJSULJSSuRaAQBQSwcIXKZJ2EQAAABQAAAAUFBQSWMAAAAIAA7giI1VAAAAAAAAAAAAAAAACQAAAGluZGV4LnB5S8nMz00sUcjJL9VLzskvy8zJ1EvOzyvOLypJzMtMSc3LL0pNzkgtSiwpycjMS9XLyc9LVahWslJQslJK5FoBQSwHCFymSdhEAAAAUAAAAA=='
            )
        )

        create_function_request = fc_20230330_models.CreateFunctionRequest(
            body=create_function_input
        )

        try:
            response = client.create_function(create_function_request)
            print("CreateFunction 调用成功!")
            print(f"状态码: {response.status_code}")
            print(f"函数信息: {response.body}")
            return True
        except Exception as e:
            print(f"CreateFunction 调用失败: {e}")
            print(f"错误类型: {type(e)}")
            import traceback
            traceback.print_exc()
            return False
    @staticmethod
    def create_simple_zip():
        """
        创建一个简单的 Python 函数 ZIP 包
        """
        # 简单的 Hello World 函数
        function_code = '''def handler(event, context):
        return {
            'statusCode': 200,
            'body': 'Hello World'
        }
        '''

        # 创建 ZIP 文件
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            zip_file.writestr('index.py', function_code)

        # 返回 base64 编码的 ZIP 内容
        zip_buffer.seek(0)
        return base64.b64encode(zip_buffer.read()).decode('utf-8')
        
    @staticmethod
    def test_update_functions():
        """
        测试 update_functions API
        """
        print("=== 测试 update_functions API ===")
    
        client = FCDemo.create_client()
        zip_content = FCDemo.create_simple_zip()
        update_function_input = fc_20230330_models.UpdateFunctionInput(
            description='Test function created via FC SDK',
            runtime='python3.9',
            handler='index.handler',
            memory_size=128,
            timeout=60,
            code=fc_20230330_models.InputCodeLocation(
                zip_file=zip_content
            )
        )

        try:
            # 创建 updateFunctions 请求
            updateFunctionRequest = fc_20230330_models.UpdateFunctionRequest(
                body=update_function_input
            )
            function_name='my-test-function-sdk1'
            response = client.update_function(function_name,updateFunctionRequest)
            print("UpdateFunctionRequest 调用成功!")
            print(f"状态码: {response.status_code}")
        except Exception as e:
            print(f"UpdateFunctionRequest 调用失败: {e}")
            return False
        return True

   

if __name__ == '__main__':
    success=FCDemo.test_list_functions()
    if success:
        print("\n🎉 测试完成！FC 3.0 API 调用成功！")
    else:
        print("\n💥 测试失败，请检查配置和权限")
        sys.exit(1)
