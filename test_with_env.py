#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 使用环境变量测试 FC 3.0 SDK

import os
import sys

# 设置测试用的环境变量（请替换为您的真实凭据）
def setup_test_env():
    """
    设置测试环境变量
    请在这里填入您的真实凭据进行测试
    """
    # 请取消注释并填入您的真实凭据
    # os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'] = 'your_access_key_id_here'
    # os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET'] = 'your_access_key_secret_here'
    # os.environ['ALIBABA_CLOUD_REGION'] = 'cn-shanghai'
    
    # 检查是否已设置
    if not os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID'):
        print("请在此文件中设置您的阿里云凭据，或者通过环境变量设置")
        print("编辑 test_with_env.py 文件，取消注释并填入真实凭据")
        return False
    return True

if __name__ == '__main__':
    if setup_test_env():
        # 导入并运行测试
        from demo_fc_sdk import FCDemo
        FCDemo.main(sys.argv[1:])
    else:
        print("\n使用方法:")
        print("1. 编辑 test_with_env.py，在 setup_test_env() 函数中填入您的凭据")
        print("2. 或者设置环境变量:")
        print("   export ALIBABA_CLOUD_ACCESS_KEY_ID='your_key'")
        print("   export ALIBABA_CLOUD_ACCESS_KEY_SECRET='your_secret'")
        print("   python demo_fc_sdk.py")
