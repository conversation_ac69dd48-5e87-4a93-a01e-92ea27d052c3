# 🎯 阿里云 FC 3.0 签名错误解决方案总结

## 🔍 问题诊断

您遇到的 `SignatureNotMatch` 错误的根本原因是：

1. **使用了错误的 SDK**: 原代码使用通用 OpenAPI SDK，而不是专门的 FC 3.0 SDK
2. **Endpoint 格式错误**: 使用了账号特定的 endpoint 而不是标准格式
3. **API 参数配置错误**: 路径、方法等配置不正确

## ✅ 最佳解决方案

### 🥇 方案1: 使用正确的 FC 3.0 SDK（强烈推荐）

**文件**: `demo_fc_sdk.py`

```python
# 安装正确的 SDK
pip install alibabacloud_fc20230330

# 使用正确的导入
from alibabacloud_fc20230330.client import Client as FC20230330Client
from alibabacloud_fc20230330 import models as fc_20230330_models
```

**优势**:
- ✅ 官方推荐的方式
- ✅ 自动处理签名和认证
- ✅ 类型安全和代码提示
- ✅ 更简洁的 API

### 🥈 方案2: 修复 OpenAPI 调用

**文件**: `demo.py`（已修复）

如果必须使用 OpenAPI 方式，主要修复：
- Endpoint: `cn-shanghai.fc.aliyuncs.com`
- 路径: `/2023-03-30/functions`
- 方法: `POST`
- 认证: `auth_type='AK'`, `style='ROA'`

## 🚀 快速开始

### 步骤1: 设置凭据

选择以下任一方式：

**方式A: 配置文件**
```bash
cp config_example.py config.py
# 编辑 config.py 填入真实凭据
```

**方式B: 环境变量**
```bash
export ALIBABA_CLOUD_ACCESS_KEY_ID="your_key"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your_secret"
```

### 步骤2: 运行测试

```bash
# 推荐：使用 FC 3.0 SDK
python demo_fc_sdk.py

# 或者：使用修复后的 OpenAPI 版本
python demo.py
```

## 📋 测试检查清单

- [ ] 安装了正确的 SDK: `alibabacloud_fc20230330`
- [ ] 设置了正确的凭据（AccessKey ID/Secret）
- [ ] 确认区域设置正确（如 cn-shanghai）
- [ ] 确认 AccessKey 有函数计算权限
- [ ] 先测试 ListFunctions 验证连接

## 🔧 故障排除

### 如果仍然遇到签名错误：

1. **检查凭据格式**
   ```bash
   # AccessKey ID 通常以 LTAI 开头
   # AccessKey Secret 是长字符串
   ```

2. **验证权限**
   - 确保 AccessKey 有 `fc:ListFunctions` 权限
   - 确保 AccessKey 有 `fc:CreateFunction` 权限

3. **检查区域**
   - 确保指定的区域支持函数计算
   - 常用区域: cn-shanghai, cn-beijing, cn-hangzhou

4. **网络检查**
   ```bash
   # 测试网络连接
   curl -I https://cn-shanghai.fc.aliyuncs.com
   ```

## 📞 获取帮助

如果问题仍然存在：

1. 运行 `demo_fc_sdk.py` 查看详细错误信息
2. 检查阿里云控制台的 AccessKey 权限设置
3. 参考阿里云官方文档：https://help.aliyun.com/zh/functioncompute/fc-3-0/

## 🎉 成功标志

当看到以下输出时，说明问题已解决：

```
=== 测试 ListFunctions API ===
ListFunctions 调用成功!
状态码: 200

=== 测试 CreateFunction API ===
CreateFunction 调用成功!
状态码: 200
```
