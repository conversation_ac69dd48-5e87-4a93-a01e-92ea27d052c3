# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys

from typing import List

from alibabacloud_tea_openapi.client import Client as OpenApiClient
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_credentials.models import Config as CredentialConfig


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> OpenApiClient:
        """
        使用凭据初始化账号Client
        @return: Client
        @throws Exception
        """
        # 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378659.html。
        credential_config = CredentialConfig(
            type='access_key',
            access_key_id=os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID'),
            access_key_secret=os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET')
        )
        credential = CredentialClient(credential_config)
        config = open_api_models.Config(
            credential=credential
        )   
        # 使用标准endpoint
        config.endpoint = '****************.cn-shanghai.fc.aliyuncs.com'
        return OpenApiClient(config)

    @staticmethod
    def create_api_info() -> open_api_models.Params:
        """
        API 相关
        @param path: string Path parameters
        @return: OpenApi.Params
        """
        params = open_api_models.Params(
            # 接口名称,
            action='DescribeRegions',
            # 接口版本,
            version='2023-03-30',
            # 接口协议,
            protocol='HTTPS',
            # 接口 HTTP 方法,
            method='GET',
            auth_type='ROA',
            style='FC',
            # 接口 PATH,
            pathname=f'/2023-03-30/regions',
            # 接口请求体内容格式,
            req_body_type='json',
            # 接口响应体内容格式,
            body_type='json'
        )
        return params

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        print(f"环境变量检查: ALIBABA_CLOUD_ACCESS_KEY_ID:"+os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID'))
        print(f"环境变量检查: ALIBABA_CLOUD_ACCESS_KEY_SECRET:"+os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET'))
        client = Sample.create_client()
        params = Sample.create_api_info()
        # runtime options
        runtime = util_models.RuntimeOptions()
        request = open_api_models.OpenApiRequest(
            headers={
                'host': '****************.cn-shanghai.fc.aliyuncs.com',
                'content-type': 'application/json'
            },
            query={
                'accountId': '****************'  # 如果需要指定账号ID
            }
        )
        # 复制代码运行请自行打印 API 的返回值
        # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        client.call_api(params, request, runtime)

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        print(f"环境变量检查: ALIBABA_CLOUD_ACCESS_KEY_ID:"+os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_ID'))
        print(f"环境变量检查: ALIBABA_CLOUD_ACCESS_KEY_SECRET:"+os.environ.get('ALIBABA_CLOUD_ACCESS_KEY_SECRET'))
        client = Sample.create_client()
        params = Sample.create_api_info()
        # runtime options
        runtime = util_models.RuntimeOptions()
        request = open_api_models.OpenApiRequest()
        # 复制代码运行请自行打印 API 的返回值
        # 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
        await client.call_api_async(params, request, runtime)


if __name__ == '__main__':
    Sample.main(sys.argv[1:])