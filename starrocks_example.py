from starrocks_query import StarRocksConnector
import logging

# 配置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(levelname)s - %(message)s')

# 数据库连接配置
DB_CONFIG = {
    "host": "your_starrocks_host",
    "port": 9030,
    "user": "your_username",
    "password": "your_password",
    "database": "your_database"
}

def main():
    # 使用with语句自动管理连接
    with StarRocksConnector(**DB_CONFIG) as sr:
        try:
            # 示例1：简单查询
            sql1 = """
                SELECT * FROM your_table 
                WHERE create_time >= '2024-01-01'
                LIMIT 5
            """
            results1 = sr.query(sql1)
            print("\n示例1 - 简单查询结果:")
            for row in results1:
                print(row)

            # 示例2：聚合查询
            sql2 = """
                SELECT 
                    date_column,
                    COUNT(*) as count,
                    SUM(amount) as total_amount
                FROM your_table
                GROUP BY date_column
                ORDER BY date_column DESC
                LIMIT 5
            """
            results2 = sr.query(sql2)
            print("\n示例2 - 聚合查询结果:")
            for row in results2:
                print(row)

        except Exception as e:
            logging.error(f"查询执行失败: {str(e)}")

if __name__ == "__main__":
    main()